# 测试配置说明

本文档说明如何通过环境变量自定义 `SporadicProjectServiceImplTest` 的测试参数。

## 测试范围

本测试类专注于测试 `SporadicProjectServiceImpl.getUserProjectList()` 方法，包括：
- 成功获取用户项目列表
- 处理空结果情况
- 处理无用户项目情况
- 参数验证和异常处理

## 配置方式

测试环境使用 `src/main/resources/bootstrap.yml` 进行配置，通过环境变量覆盖默认值。

## 环境变量支持

以下环境变量可以设置测试参数：

### 核心全局参数

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `TEST_ORGANIZATION_ID` | `test-org-id` | 组织机构ID |
| `TEST_SCID` | `test-scid` | 系统/服务标识 |
| `Authorization` | `Bearer Authorization` | 认证令牌 |



## 使用示例

### 1. 使用测试脚本（推荐）

```bash
# 创建本地环境变量文件
cp .env.test.local.example .env.test.local

# 编辑配置文件
vim .env.test.local

# 运行测试
./scripts/run-tests-with-env.sh
```

### 2. 手动创建环境变量文件

```bash
# 创建 .env.test.local 文件
echo 'TEST_ORGANIZATION_ID=my-org-123' > .env.test.local
echo 'TEST_SCID=my-scid' >> .env.test.local
echo 'Authorization=Bearer my-token' >> .env.test.local

# 运行测试脚本
./scripts/run-tests-with-env.sh
```

### 3. 直接通过环境变量运行测试

```bash
# 设置环境变量
export TEST_ORGANIZATION_ID=my-org-123
export TEST_SCID=my-service-scid
export Authorization="Bearer my-custom-token"

# 运行测试
mvn test -Dtest=SporadicProjectServiceImplTest
```

### 4. 在IDE中设置环境变量

在IntelliJ IDEA中：
1. 打开 Run/Debug Configurations
2. 选择测试配置
3. 在 Environment variables 中添加所需的环境变量

## 配置优先级

配置的优先级从高到低为：
1. 环境变量（从 .env.test.local 文件加载）
2. 系统属性 (-D参数)
3. bootstrap.yml 配置文件
4. 代码中的默认值

## 测试方法说明

### getUserProjectList 相关测试

| 测试方法 | 测试场景 | 说明 |
|---------|---------|------|
| `testGetUserProjectList_WithRegionAndDto_Success` | 正常获取项目列表 | 验证带区域参数的成功调用 |
| `testGetUserProjectList_WithRegionAndDto_NoUserProject` | 无用户项目 | 验证用户无项目时返回空列表 |
| `testGetUserProjectList_WithDto_Success` | 无区域参数成功调用 | 验证不带区域参数的成功调用 |
| `testGetUserProjectList_EmptyResult` | 空结果处理 | 验证查询结果为空的情况 |
| `testGetUserProjectList_WithNullRegion` | 空区域参数 | 验证区域参数为null的处理 |

### 配置验证测试

| 测试方法 | 测试场景 | 说明 |
|---------|---------|------|
| `testConfigurationLoading` | 配置加载验证 | 验证测试配置是否正确加载 |
| `testEnvironmentVariableOverride` | 环境变量覆盖 | 验证环境变量是否正确覆盖默认值 |

## 注意事项

1. 所有配置都有合理的默认值，即使不设置任何环境变量，测试也能正常运行
2. 环境变量名称区分大小写
3. 测试专注于 `getUserProjectList` 方法，其他方法已被移除以简化维护
4. 如果环境变量值格式不正确，将使用默认值并在日志中输出警告

## 故障排除

如果测试运行异常，请检查：
1. 环境变量名称是否正确
2. 环境变量值的格式是否正确
3. 查看测试日志中的配置加载信息
4. 确认 `getUserProjectList` 方法的依赖项是否正确模拟
