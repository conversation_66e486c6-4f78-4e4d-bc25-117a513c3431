#!/bin/bash

# 测试运行脚本 - 使用环境变量自定义测试参数
# 用法: ./scripts/run-tests-with-env.sh

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "=========================================="
echo "运行 SporadicProjectServiceImplTest"
echo "项目目录: $PROJECT_DIR"
echo "=========================================="

# 检查并加载环境变量文件
ENV_FILE="$PROJECT_DIR/.env.test.local"

if [ -f "$ENV_FILE" ]; then
    echo "✓ 找到环境变量文件: .env.test.local"
    echo "加载环境变量..."
    source "$ENV_FILE"
else
    echo "❌ 未找到环境变量文件: .env.test.local"
    echo ""
    echo "请创建 .env.test.local 文件并设置以下环境变量："
    echo ""
    echo "# 复制以下内容到 .env.test.local 文件"
    echo "TEST_ORGANIZATION_ID=your-org-id"
    echo "TEST_SCID=your-scid"
    echo "Authorization=Bearer your-token"
    echo ""
    echo "示例："
    echo "echo 'TEST_ORGANIZATION_ID=my-org-123' > .env.test.local"
    echo "echo 'TEST_SCID=my-scid' >> .env.test.local"
    echo "echo 'Authorization=Bearer my-token' >> .env.test.local"
    echo ""
    echo "然后重新运行此脚本。"
    exit 1
fi

# 显示当前配置
echo ""
echo "当前测试配置:"
echo "  组织ID: ${TEST_ORGANIZATION_ID:-'默认值'}"
echo "  服务标识: ${TEST_SCID:-'默认值'}"
echo "  认证令牌: ${Authorization:-'默认值'}"
echo ""

# 切换到项目目录
cd "$PROJECT_DIR"

# 运行测试
echo "开始运行测试..."
mvn test -Dtest=SporadicProjectServiceImplTest -Dspring.profiles.active=test

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "✅ 测试运行成功！"
    echo "=========================================="
else
    echo ""
    echo "=========================================="
    echo "❌ 测试运行失败！"
    echo "=========================================="
    exit 1
fi
